import Foundation
import UIKit

actor RecipeGenerationService: RecipeGenerationServiceProtocol {
    private let geminiService = GeminiAPIService()
    
    func generateRecipes(from ingredients: [String]) async throws -> [Recipe] {
        let prompt = createRecipePrompt(from: ingredients)
        
        do {
            // Use extractIngredients method as a workaround for text processing
            // This is a temporary solution - we'll create a simple text processing method
            let jsonResponse = try await processRecipeText(prompt)
            
            // Extract JSON from the response
            guard let jsonData = extractJSON(from: jsonResponse) else {
                throw RecipeGenerationError.invalidJSONResponse
            }
            
            // Decode recipes from JSON
            let recipeData = try JSONDecoder().decode([RecipeData].self, from: jsonData)
            
            // Convert to Recipe objects
            let recipes = recipeData.map { data in
                Recipe(
                    recipeTitle: data.title,
                    description: data.description,
                    ingredients: data.ingredients,
                    instructions: data.instructions,
                    nutrition: Recipe.NutritionInfo(
                        calories: data.nutrition.calories,
                        protein: data.nutrition.protein,
                        carbs: data.nutrition.carbs,
                        fat: data.nutrition.fat
                    ),
                    cookingTime: data.cookingTime,
                    servings: data.servings,
                    difficulty: Recipe.Difficulty(rawValue: data.difficulty) ?? .medium
                )
            }
            
            return recipes
        } catch {
            throw RecipeGenerationError.processingFailed(error.localizedDescription)
        }
    }
    
    func generateMealIdeas(from ingredients: [String], preferences: RecipePreferences) async throws -> [RecipeIdea] {
        // Build prompt with preferences
        let prompt = createRecipePrompt(from: ingredients, preferences: preferences)
        let jsonResponse = try await processRecipeText(prompt)
        guard let jsonData = extractJSON(from: jsonResponse) else {
            throw RecipeGenerationError.invalidJSONResponse
        }
        let recipeData = try JSONDecoder().decode([RecipeData].self, from: jsonData)
        var recipes = recipeData.map { data in
            Recipe(
                recipeTitle: data.title,
                description: data.description,
                ingredients: data.ingredients,
                instructions: data.instructions,
                nutrition: Recipe.NutritionInfo(
                    calories: data.nutrition.calories,
                    protein: data.nutrition.protein,
                    carbs: data.nutrition.carbs,
                    fat: data.nutrition.fat
                ),
                cookingTime: data.cookingTime,
                servings: data.servings,
                difficulty: Recipe.Difficulty(rawValue: data.difficulty) ?? .medium
            )
        }
        
        // Safety post-filter by preferences
        if preferences.respectRestrictions {
            let blocked = Set((preferences.allergiesAndIntolerances + preferences.strictExclusions).map { $0.lowercased() })
            recipes = recipes.filter { recipe in
                let ing = Set(recipe.ingredients.map { $0.lowercased() })
                return blocked.isDisjoint(with: ing)
            }
        }
        
        return recipes.map { recipe in
            RecipeIdea(
                recipe: recipe,
                status: .readyToCook,
                missingIngredients: []
            )
        }
    }
    
    // MARK: - Private Helper Methods
    
    private func processRecipeText(_ prompt: String) async throws -> String {
        // Create a simple request to Gemini API for recipe generation
        let requestBody: [String: Any] = [
            "contents": [
                [
                    "parts": [
                        ["text": prompt]
                    ]
                ]
            ],
            "generationConfig": [
                "temperature": 0.7,
                "topK": 40,
                "topP": 0.95,
                "maxOutputTokens": 2048,
                "response_mime_type": "application/json"
            ]
        ]
        
        guard let jsonData = try? JSONSerialization.data(withJSONObject: requestBody),
              let url = URL(string: "https://generativelanguage.googleapis.com/v1beta/models/gemini-1.5-flash-latest:generateContent?key=\(APIKeys.geminiAPIKey)") else {
            throw RecipeGenerationError.invalidRequest
        }
        
        var request = URLRequest(url: url)
        request.httpMethod = "POST"
        request.setValue("application/json", forHTTPHeaderField: "Content-Type")
        request.httpBody = jsonData
        
        let (data, _) = try await URLSession.shared.data(for: request)
        
        // Parse Gemini response
        guard let response = try? JSONSerialization.jsonObject(with: data) as? [String: Any],
              let candidates = response["candidates"] as? [[String: Any]],
              let firstCandidate = candidates.first,
              let content = firstCandidate["content"] as? [String: Any],
              let parts = content["parts"] as? [[String: Any]],
              let firstPart = parts.first,
              let text = firstPart["text"] as? String else {
            throw RecipeGenerationError.invalidResponse
        }
        
        return text
    }
    
    private func createRecipePrompt(from ingredients: [String]) -> String {
        return """
        Generate 3 healthy recipes using these ingredients: \(ingredients.joined(separator: ", ")).
        
        Return the response as a JSON array with this exact structure:
        [
          {
            "title": "Recipe Name",
            "description": "Brief description",
            "ingredients": ["ingredient 1", "ingredient 2"],
            "instructions": ["step 1", "step 2"],
            "cookingTime": "30 minutes",
            "servings": 4,
            "difficulty": "medium",
            "nutrition": {
              "calories": "350",
              "protein": "25g",
              "carbs": "30g",
              "fat": "15g"
            }
          }
        ]
        
        Use only the provided ingredients plus common pantry staples like salt, pepper, oil, etc.
        Make sure difficulty is one of: "easy", "medium", "hard".
        """
    }
    
    private func createRecipePrompt(from ingredients: [String], preferences: RecipePreferences) -> String {
        var constraints: [String] = []
        if !preferences.dietaryRestrictions.isEmpty {
            constraints.append("Dietary preferences: \(preferences.dietaryRestrictions.joined(separator: ", "))")
        }
        if preferences.respectRestrictions {
            if !preferences.allergiesAndIntolerances.isEmpty {
                constraints.append("Allergies/Intolerances: strictly avoid \(preferences.allergiesAndIntolerances.joined(separator: ", "))")
            }
            if !preferences.strictExclusions.isEmpty {
                constraints.append("Do NOT include: \(preferences.strictExclusions.joined(separator: ", "))")
            }
        }
        let constraintsText = constraints.isEmpty ? "" : "\nConstraints:\n- " + constraints.joined(separator: "\n- ") + "\n"
        // Include equipment only when user selected some; otherwise omit the line
        let equipmentText = preferences.equipmentOwned.isEmpty ? "" : "\nAvailable equipment: \(preferences.equipmentOwned.joined(separator: ", ")).\n"

        return """
        Generate 3 healthy recipes using these ingredients: \(ingredients.joined(separator: ", ")).
        Target servings: \(preferences.numberOfServings). Target cooking time: ~\(preferences.cookingTimeInMinutes) minutes.
        \(constraintsText)\(equipmentText)
        Return the response as a JSON array with this exact structure:
        [
          {
            "title": "Recipe Name",
            "description": "Brief description",
            "ingredients": ["ingredient 1", "ingredient 2"],
            "instructions": ["step 1", "step 2"],
            "cookingTime": "30 minutes",
            "servings": \(preferences.numberOfServings),
            "difficulty": "medium",
            "nutrition": {
              "calories": "350",
              "protein": "25g",
              "carbs": "30g",
              "fat": "15g"
            }
          }
        ]
        Only use the provided ingredients (plus common staples like salt, pepper, oil). Absolutely avoid any listed allergens or strict exclusions.
        Ensure difficulty is one of: "easy", "medium", "hard".
        """
    }
    
    private func extractJSON(from text: String) -> Data? {
        // Look for JSON array in the response
        guard let startIndex = text.firstIndex(of: "["),
              let endIndex = text.lastIndex(of: "]") else {
            return nil
        }
        
        let jsonString = String(text[startIndex...endIndex])
        return jsonString.data(using: .utf8)
    }
}

// MARK: - Supporting Types

struct RecipeData: Codable {
    let title: String
    let description: String
    let ingredients: [String]
    let instructions: [String]
    let cookingTime: String
    let servings: Int
    let difficulty: String
    let nutrition: NutritionData
}

struct NutritionData: Codable {
    let calories: String
    let protein: String
    let carbs: String
    let fat: String
}

enum RecipeGenerationError: Error, LocalizedError {
    case invalidRequest
    case invalidResponse
    case invalidJSONResponse
    case processingFailed(String)
    case noPantryItems
    
    var errorDescription: String? {
        switch self {
        case .invalidRequest:
            return "Invalid request format"
        case .invalidResponse:
            return "Invalid response from API"
        case .invalidJSONResponse:
            return "Could not parse JSON response"
        case .processingFailed(let message):
            return "Processing failed: \(message)"
        case .noPantryItems:
            return "No pantry items available for recipe generation"
        }
    }
} 
