# V6 PRD — Recipe Generation History & Enhanced Display with Smart Save

Owner: Augment Agent  
Date: 2025-01-XX  
Target: iOS 17+, Swift 5.9+, Xcode 15+  
Scope: Recipe generation history, tab-based interface, smart save mechanisms, user control features, structured meal plan generation

## 1) Executive Summary

V6 introduces a comprehensive recipe history system that transforms the current single-view recipe display into a dual-tab experience. Users can now access their recent Quick Results and navigate through historical Meal Plans with intuitive swipe gestures. The system includes smart save mechanisms, user control features, performance optimizations, and **critically fixes the meal plan generation architecture** to ensure proper date-based slot allocation.

## 2) Core Objectives

- **Simple User Experience**: No complex time logic that restricts users
- **Generation History**: Save and display the most recent 3 recipe generations with smart save strategies
- **Clear Time Display**: Show when recipes were generated, not when they "should" be consumed
- **User Freedom**: Allow users to generate any meal type at any time without system interference
- **Enhanced Meal Plan Display**: Replace list view with intuitive swipeable 7-day calendar matrix
- **Historical Navigation**: Easy access to past and future meal plans through natural swipe gestures
- **Clean Interface Design**: Separate Quick Results and Meal Plans into distinct, focused experiences
- **Smart Save System**: Intelligent save mechanisms with user control and optimization
- **🎯 Structured Generation**: Fix meal plan generation to use proper date-based slot allocation instead of post-generation grouping

## 3) Interface Architecture - Tab-Based Design

### 3.1 Top-Level Structure
- **Segmented control** within Recipes page (top placement)
- **Two distinct modes**:
  - **"Recent" Tab**: Quick Results history (3 most recent generations)
  - **"Plans" Tab**: Meal Plan calendar with swipe navigation

### 3.2 Visual Implementation
```
┌─────────────────────────┐
│      📖 Recipes         │ <- Navigation Title
├─────────────────────────┤
│ Recent │ Plans          │ <- Top Segmented Control
├─────────────────────────┤
│                         │
│    Tab Content Area     │
│                         │
│                         │
└─────────────────────────┘
```

### 3.3 Design Rationale
- Follows iOS design patterns (Apple Music, App Store, Photos)
- Clear visual hierarchy: page-level sub-categories, not global navigation
- Maximizes content area by using top placement
- Avoids conflict with main app TabBar
- Thumb-friendly positioning for single-hand operation

## 4) Recent Tab - Quick Results History

### 4.1 Display Features
- **Card-based layout** for the most recent 3 recipe generations
- Each card displays:
  - Generation date and time (e.g., "9月1日 14:00")
  - Meal type and dish count (e.g., "午餐 · 2道菜")
  - Recipe titles preview
  - Save status indicator (saved/unsaved)

### 4.2 User Interactions
- **Tap card**: View full recipe details
- **Long press**: Show context menu with options:
  - Save to favorites
  - Delete from history
  - Share recipes
  - Regenerate with same parameters

### 4.3 Smart Save Features
- **Save confirmation**: "保存到历史记录？" after generation
- **Replace prompt**: If user regenerates immediately, ask "替换上一个结果？"
- **Favorite marking**: Star icon for user-marked favorites

## 5) Plans Tab - Meal Plan Calendar

### 5.1 Calendar Matrix
- **Swipeable 7-day calendar matrix**
- **Horizontal swipe navigation**:
  - 左滑：查看下一周的meal plan
  - 右滑：查看上一周的meal plan
  - 最多可查看1个月内的所有周次 (4周历史)

### 5.2 Week Navigation
- **Week indicator**: Show current week date range at top (e.g., "Jan 1-7, 2025" or "本周")
- **Smooth transitions**: Animated week-to-week navigation with natural swipe gestures

### 5.3 Calendar Layout
```
┌─────────────────────────────────────┐
│        Jan 1-7, 2025 (本周)         │
├─────────────────────────────────────┤
│           │Breakfast│ Lunch │Dinner │
├─────────────────────────────────────┤
│ Monday    │    ●    │   ●   │   ●   │
│ Tuesday   │    ●    │   ○   │   ●   │
│ Wednesday │    ○    │   ●   │   ●   │
│ Thursday  │    ●    │   ●   │   ○   │
│ Friday    │    ●    │   ○   │   ●   │
│ Saturday  │    ●    │   ●   │   ●   │
│ Sunday    │    ●    │   ●   │   ●   │
└─────────────────────────────────────┘
```

### 5.4 Visual Indicators
- ● = Has meal plan for this slot (tappable)
- ○ = No meal plan for this slot
- ⭐ = Favorited meal plan
- 🔄 = Recently regenerated

### 5.5 Interactive Features
- **Tap filled slots (●)**: View detailed recipes
- **Long press slots**: Context menu with options:
  - Mark as favorite
  - Regenerate meal
  - Copy to another day
  - Delete meal plan
- **Empty slots (○)**: Show "未安排餐食" with option to generate

## 6) 🎯 Critical Fix: Structured Meal Plan Generation

### 6.1 Current Architecture Problem ❌
```swift
// BROKEN: Generate flat array, then force-group by index
generateMealIdeas() -> [RecipeUIModel] // No date/meal association
RecipeGrouper.group(items, days: 2, selectedMeals: [.breakfast, .dinner]) // Wrong!
```

### 6.2 Correct Architecture ✅
```swift
struct MealPlanGenerationRequest {
    let startDate: Date           // Specific start date
    let days: Int                 // Number of days (1-7)
    let selectedMeals: [MealType] // Selected meal types
    let mealConfigurations: [MealType: MealConfig]
    
    // Generate specific slot requirements
    var mealSlots: [MealSlotRequest] {
        var slots: [MealSlotRequest] = []
        let calendar = Calendar.current
        
        for dayOffset in 0..<days {
            let date = calendar.date(byAdding: .day, value: dayOffset, to: startDate)!
            
            for mealType in selectedMeals {
                let config = mealConfigurations[mealType]!
                slots.append(MealSlotRequest(
                    date: date,
                    dayIndex: dayOffset,
                    mealType: mealType,
                    dishCount: config.numberOfDishes,
                    maxCookingTime: config.cookingTimeMinutes
                ))
            }
        }
        return slots
    }
}

struct MealSlotRequest {
    let date: Date        // Exact date: 2025-01-15
    let dayIndex: Int     // Relative index: 0, 1, 2...
    let mealType: MealType // breakfast, lunch, dinner
    let dishCount: Int    // Required dishes for this slot
    let maxCookingTime: Int
}
```

### 6.3 Date Range Constraints
```swift
struct MealPlanDateRange {
    static let minStartDate: TimeInterval = 0        // Today
    static let maxStartDate: TimeInterval = 7 * 24 * 60 * 60  // Max 7 days future
    static let maxPlanDuration: Int = 7              // Max 7 days duration
    
    static func validDateRange(from today: Date = Date()) -> ClosedRange<Date> {
        let calendar = Calendar.current
        let minDate = today
        let maxDate = calendar.date(byAdding: .day, value: 7, to: today)!
        return minDate...maxDate
    }
}
```

### 6.4 Structured Generation Service
```swift
func generateMealPlan(request: MealPlanGenerationRequest) async throws -> MealPlan {
    var dayPlans: [DayPlan] = []
    
    // Generate by day
    for dayOffset in 0..<request.days {
        let date = Calendar.current.date(byAdding: .day, value: dayOffset, to: request.startDate)!
        var mealSlots: [MealSlot] = []
        
        // Generate by meal type
        for mealType in request.selectedMeals {
            let config = request.mealConfigurations[mealType]!
            
            // Generate recipes for this specific slot
            let recipes = try await generateRecipesForSlot(
                date: date,
                mealType: mealType,
                dishCount: config.numberOfDishes,
                maxTime: config.cookingTimeMinutes
            )
            
            // Each recipe knows its exact assignment
            let recipesWithMetadata = recipes.map { recipe in
                var r = recipe
                r.assignedDate = date
                r.assignedMealType = mealType
                r.dayIndex = dayOffset
                return r
            }
            
            mealSlots.append(MealSlot(
                slotId: UUID(),
                date: date,
                dayIndex: dayOffset,
                mealType: mealType,
                recipes: recipesWithMetadata
            ))
        }
        
        dayPlans.append(DayPlan(date: date, dayIndex: dayOffset, meals: mealSlots))
    }
    
    return MealPlan(startDate: request.startDate, days: dayPlans)
}
```

### 6.5 Data Models
```swift
struct MealPlan {
    let startDate: Date
    let days: [DayPlan]
}

struct DayPlan {
    let date: Date        // 2025-01-15
    let dayIndex: Int     // 0
    let meals: [MealSlot] // All meals for this day
}

struct MealSlot {
    let slotId: UUID
    let date: Date        // 2025-01-15
    let dayIndex: Int     // 0
    let mealType: MealType // breakfast
    let recipes: [RecipeUIModel] // Exact recipes for this slot
}
```

### 6.6 Default Configuration
```swift
struct DefaultMealPlanConfig {
    static func createDefault() -> MealPlanGenerationRequest {
        let today = Date()
        let tomorrow = Calendar.current.date(byAdding: .day, value: 1, to: today)!
        
        return MealPlanGenerationRequest(
            startDate: tomorrow,  // Default: start tomorrow
            days: 3,             // Default: 3 days
            selectedMeals: [.lunch, .dinner], // Default: lunch + dinner
            mealConfigurations: [
                .lunch: MealConfig(cookingTimeMinutes: 30, numberOfDishes: 2),
                .dinner: MealConfig(cookingTimeMinutes: 45, numberOfDishes: 2)
            ]
        )
    }
}
```

## 7) Smart Save System

### 7.1 Intelligent Save Strategies
```swift
enum SaveStrategy {
    case automatic      // Auto-save all generations
    case confirmPrompt  // Ask user before saving
    case smartDetection // Detect user satisfaction and save accordingly
}
```

### 7.2 Save Confirmation Flow
1. User generates recipes
2. System shows results
3. Prompt appears: "保存到历史记录？"
   - "保存" - Save to history
   - "收藏" - Save and mark as favorite
   - "跳过" - Don't save, keep in temporary view

### 7.3 Smart Detection Logic
- **Immediate regeneration**: Likely unsatisfied, ask to replace
- **View recipe details**: Likely interested, auto-save
- **Share recipes**: Definitely satisfied, auto-save and mark favorite
- **Quick exit**: Likely unsatisfied, don't save

### 7.4 Lightweight Storage Model
```swift
struct LightweightHistory {
    let id: UUID
    let timestamp: Date
    let mealType: MealType
    let recipeCount: Int
    let recipeTitles: [String]     // Quick preview
    let isFavorite: Bool
    let fullDataKey: String?       // Lazy load full data
    let generationContext: GenerationContext
}

struct GenerationContext {
    let pantrySnapshot: [String]   // Ingredient names only
    let userPreferences: UserPreferences
    let searchQuery: String?
    let mode: GenerationMode
    let dateRange: DateRange?      // For meal plans
}
```

## 8) User Control Features

### 8.1 History Management
- **Delete individual items**: Long press → Delete
- **Clear all history**: Settings → "清空历史记录"
- **Export favorites**: Share selected favorite recipes
- **Batch operations**: Select multiple items for bulk actions

### 8.2 Favorites System
- **Star marking**: Tap star icon to favorite/unfavorite
- **Favorites filter**: Show only favorited items
- **Favorites persistence**: Never auto-deleted, user-controlled only

### 8.3 Context Menu Actions
```swift
enum HistoryAction {
    case viewDetails
    case addToFavorites
    case removeFromFavorites
    case regenerateWithSameParams
    case shareRecipes
    case copyToMealPlan
    case deleteFromHistory
}
```

## 9) Performance Optimizations

### 9.1 Storage Strategy
- **Memory cache**: Recent 10 items in memory
- **Disk persistence**: Full history on disk with lazy loading
- **Background saving**: Non-blocking save operations
- **Batch operations**: Group multiple saves to reduce I/O

### 9.2 Data Loading
- **Progressive loading**: Load titles first, details on demand
- **Image caching**: Cache recipe images with TTL
- **Pagination**: Load older history in chunks

### 9.3 Memory Management
```swift
class HistoryManager {
    private let memoryCache = NSCache<NSString, HistoryItem>()
    private let backgroundQueue = DispatchQueue(label: "history.save")
    private let diskStorage: DiskStorageProtocol
    
    func saveHistory(_ item: HistoryItem, strategy: SaveStrategy) async {
        // Background save with strategy
    }
}
```

## 10) Technical Implementation

### 10.1 Data Models
```swift
struct RecipeGenerationHistory {
    let id: UUID
    let generatedAt: Date
    let mode: UIMode
    let mealType: MealType?
    let recipes: [RecipeUIModel]
    let dishCount: Int
    let mealPlan: MealPlan?
    let isFavorite: Bool
    let saveStrategy: SaveStrategy
    let generationContext: GenerationContext
}

struct WeeklyMealPlan {
    let weekStartDate: Date
    let weekEndDate: Date
    let mealPlan: MealPlan
    let generatedAt: Date
    let isFavorite: Bool
    let metadata: MealPlanMetadata
}

enum RecipeHistoryTab: CaseIterable {
    case recent
    case plans
    
    var title: String {
        switch self {
        case .recent: return "Recent"
        case .plans: return "Plans"
        }
    }
}
```

### 10.2 Core Components
- `RecipeHistoryTabView`: Main container with segmented control
- `RecentGenerationsView`: Shows history cards for quick results
- `GenerationHistoryCard`: Individual card component with context menu
- `SwipeableMealPlanCalendarView`: 7-day table matrix with swipe navigation
- `WeekIndicatorView`: Shows current week date range
- `MealSlotIndicator`: Visual indicator for meal availability
- `WeekTransitionAnimator`: Handles smooth week-to-week animations
- `SmartSaveManager`: Handles intelligent save strategies
- `HistoryStorageService`: Manages data persistence and caching
- `StructuredMealPlanGenerator`: **NEW** - Handles date-based slot generation
- `MealPlanDatePicker`: **NEW** - Date range selection with constraints

### 10.3 Save Flow Architecture
```swift
protocol SaveStrategyProtocol {
    func shouldSave(for context: GenerationContext) async -> Bool
    func saveWithConfirmation(_ item: HistoryItem) async -> Bool
}

class SmartSaveStrategy: SaveStrategyProtocol {
    func shouldSave(for context: GenerationContext) async -> Bool {
        // Implement smart detection logic
    }
}
```

## 11) User Experience Flows

### 11.1 Tab-Based Navigation
1. User enters Recipes page
2. Sees segmented control with "Recent | Plans" at top
3. Default view shows Recent tab (most commonly used)
4. User can switch to Plans tab for meal planning overview
5. Each tab maintains its own state and scroll position

### 11.2 Smart Save Flow
1. User generates recipes (Quick or Meal Plan)
2. System analyzes user behavior and context
3. Based on strategy:
   - **Auto-save**: Silently save to history
   - **Confirm**: Show save confirmation dialog
   - **Smart**: Detect satisfaction and save accordingly
4. User can manually favorite important results
5. History appears in appropriate tab with proper indicators

### 11.3 Structured Meal Plan Generation Flow
1. User selects "Meal Plan" mode
2. System shows date picker with constraints (today + 7 days max)
3. User selects start date and duration (1-7 days)
4. User selects meal types and configurations
5. System generates recipes for each specific date+meal slot
6. Results display in calendar matrix with exact slot assignments
7. Each slot contains recipes generated specifically for that date+meal

### 11.4 History Management Flow
1. User views history in Recent or Plans tab
2. Long press on any item to show context menu
3. Available actions based on item type and status
4. Batch operations available through edit mode
5. Settings page provides global history management

## 12) Benefits & Value Proposition

### 12.1 User Benefits
- **Predictable**: Users see exactly when they generated what
- **Simple**: No hidden logic or time calculations
- **Flexible**: Generate any meal at any time
- **Focused**: Each tab serves a specific use case without clutter
- **Controlled**: Users have full control over their history
- **Intelligent**: System learns from user behavior
- **Efficient**: Fast loading and smooth interactions
- **🎯 Accurate**: Meal plans have proper date-slot correspondence

### 12.2 Technical Benefits
- **Scalable**: Efficient storage and loading strategies
- **Maintainable**: Clear separation of concerns
- **Extensible**: Easy to add features to each tab independently
- **Performant**: Optimized for memory and disk usage
- **Reliable**: Robust error handling and data integrity
- **🎯 Correct**: Fixes fundamental architecture flaw in meal plan generation

## 13) Implementation Roadmap

### 13.1 Phase 1 (P0) - Core Structure & Critical Fix
- [ ] **🎯 Fix meal plan generation architecture** - Replace post-generation grouping with structured slot-based generation
- [ ] Implement `MealPlanGenerationRequest` with date constraints
- [ ] Create `StructuredMealPlanGenerator` service
- [ ] Add date picker with 7-day constraint
- [ ] Basic tab structure with segmented control
- [ ] Recent tab with simple card layout
- [ ] Basic save functionality with confirmation
- [ ] Static meal plan calendar matrix view

### 13.2 Phase 2 (P1) - Enhanced Features
- [ ] Smart save strategies implementation
- [ ] Swipeable week navigation with animations
- [ ] Week indicator and date range display
- [ ] Context menus and user control features
- [ ] Proper slot-to-recipe assignment in calendar display

### 13.3 Phase 3 (P2) - Advanced Features
- [ ] Interactive meal slot navigation to recipe details
- [ ] Favorites system with filtering
- [ ] Performance optimizations and caching
- [ ] 1-month storage and automatic cleanup
- [ ] Quick plan options (tomorrow 3 days, this weekend, etc.)

### 13.4 Phase 4 (P3) - Polish & Extensions
- [ ] Advanced animations and gesture refinements
- [ ] Export and sharing capabilities
- [ ] Analytics and usage insights
- [ ] Accessibility enhancements

## 14) Success Metrics

### 14.1 User Engagement
- History view usage rate > 60%
- Average time spent in history views
- Favorite marking rate > 20%
- Recipe regeneration from history rate
- **🎯 Meal plan accuracy**: 0% slot misassignment errors

### 14.2 Technical Performance
- History loading time < 500ms
- Memory usage < 50MB for full history
- Save operation completion rate > 99%
- Crash rate < 0.1% in history features
- **🎯 Generation accuracy**: 100% correct date-slot mapping

### 14.3 User Satisfaction
- User retention after history feature introduction
- Support tickets related to history management
- App store reviews mentioning history features
- User feedback on save strategies
- **🎯 Reduced confusion**: Fewer support tickets about "wrong recipes in wrong slots"

## 15) Risk Assessment & Mitigation

### 15.1 Technical Risks
- **Storage growth**: Mitigated by automatic cleanup and lightweight storage
- **Performance degradation**: Mitigated by caching and lazy loading
- **Data corruption**: Mitigated by atomic operations and backup strategies
- **🎯 Migration complexity**: Mitigated by careful data model versioning and migration scripts

### 15.2 UX Risks
- **Feature complexity**: Mitigated by progressive disclosure and clear UI
- **User confusion**: Mitigated by familiar iOS patterns and clear labeling
- **Save strategy annoyance**: Mitigated by smart detection and user preferences
- **🎯 Date constraint frustration**: Mitigated by clear messaging and sensible defaults

### 15.3 Business Risks
- **Development timeline**: Mitigated by phased implementation approach
- **Resource allocation**: Mitigated by clear priority levels and MVP definition
- **User adoption**: Mitigated by gradual rollout and user feedback integration

## 16) Future Considerations

### 16.1 Potential Enhancements
- **iCloud sync**: Cross-device history synchronization
- **Recipe collections**: User-created recipe collections
- **Social sharing**: Share favorite meal plans with friends
- **AI recommendations**: Personalized recipe suggestions based on history
- **🎯 Advanced planning**: Extend date range based on user feedback

### 16.2 Integration Opportunities
- **Calendar app**: Export meal plans to system calendar
- **Health app**: Nutrition tracking integration
- **Grocery apps**: Shopping list generation from meal plans
- **Voice assistants**: Siri shortcuts for common history actions

---

*This PRD represents a comprehensive approach to recipe history management that balances user needs, technical feasibility, and business objectives while maintaining the simplicity and elegance of the existing app experience. **The critical architectural fix ensures meal plans are generated with proper date-slot correspondence, eliminating the fundamental flaw in the current post-generation grouping approach.***
